"""
弹窗处理工具 - 简化版本
基于UIAutomator2的弹窗检测和处理工具，提供简洁易用的API
参考tools/pop_example.py实现，适配项目代码风格
"""
import time
import uiautomator2 as u2
from typing import Optional, List, Dict, Any, Callable
from core.logger import log


class PopupTool:
    """弹窗处理工具类 - 简化版本"""

    def __init__(self, device: u2.Device = None):
        """
        初始化弹窗处理工具
        
        Args:
            device: UIAutomator2设备对象，如果为None则使用默认连接
        """
        if device is None:
            self.device = u2.connect()
        else:
            self.device = device

        # 常见的关闭按钮文本
        self.close_texts = [
            '关闭', '取消', '确定', '同意', '知道了', '我知道了',
            '立即体验', '稍后再说', '跳过', '继续', '允许', '拒绝',
            '好的', '明白', '下次再说', '暂不需要', '以后再说',
            'X', '×', '✕', 'Close', 'OK', 'Cancel', 'Skip',
            'Allow', 'Deny', 'Later', 'Continue', 'Got it',
            'Dismiss', 'Accept', 'Decline', 'Not now'
        ]

        # 常见的关闭按钮resource-id
        self.close_ids = [
            'android:id/button1',  # 系统对话框确定按钮
            'android:id/button2',  # 系统对话框取消按钮
            'android:id/button3',  # 系统对话框第三个按钮
            'com.transsion.aivoiceassistant:id/close_iv',  #
            'com.android.packageinstaller:id/permission_allow_button',
            'com.android.packageinstaller:id/permission_deny_button',
            'android:id/aerr_close',  # ANR对话框关闭按钮
            'android:id/aerr_restart',  # ANR对话框重启按钮
        ]

        # 弹窗类名
        self.popup_classes = [
            'android.app.Dialog',
            'android.app.AlertDialog',
            'android.widget.PopupWindow',
            'android.support.v7.app.AlertDialog',
            'androidx.appcompat.app.AlertDialog',
        ]

    def detect_and_close_popup(self, timeout: int = 10, check_interval: float = 0.5) -> bool:
        """
        检测并关闭弹窗
        
        Args:
            timeout: 超时时间（秒）
            check_interval: 检查间隔（秒）
            
        Returns:
            bool: 是否成功处理弹窗
        """
        start_time = time.time()
        log.info(f"开始检测弹窗，超时时间: {timeout}秒")

        while time.time() - start_time < timeout:
            try:
                # 1. 首先尝试通过UI层次结构查找
                if self._try_close_by_ui_hierarchy():
                    log.info("通过UI层次结构成功关闭弹窗")
                    return True

                # 2. 尝试通过坐标位置查找（右上角关闭按钮）
                if self._try_close_by_position():
                    log.info("通过位置检测成功关闭弹窗")
                    return True

                # 3. 尝试返回键
                if self.is_popup_present() and self._try_back_key():
                    log.info("通过返回键成功关闭弹窗")
                    return True

                time.sleep(check_interval)

            except Exception as e:
                log.warning(f"弹窗检测过程中出现异常: {e}")
                time.sleep(check_interval)

        log.debug(f"弹窗检测超时，未发现可处理的弹窗")
        return False

    def _try_close_by_ui_hierarchy(self) -> bool:
        """通过UI层次结构查找关闭按钮"""
        try:
            # 1. 通过resource-id查找
            for res_id in self.close_ids:
                element = self.device(resourceId=res_id)
                if element.exists(timeout=0.5):
                    log.debug(f"找到关闭按钮 (resource-id): {res_id}")
                    element.click()
                    time.sleep(0.5)
                    return True

            # 2. 通过文本查找
            for text in self.close_texts:
                # 精确匹配
                element = self.device(text=text)
                if element.exists(timeout=0.5):
                    log.debug(f"找到关闭按钮 (文本): {text}")
                    element.click()
                    time.sleep(0.5)
                    return True

                # 包含匹配
                element = self.device(textContains=text)
                if element.exists(timeout=0.5):
                    log.debug(f"找到关闭按钮 (包含文本): {text}")
                    element.click()
                    time.sleep(0.5)
                    return True

            # 3. 通过className查找常见的关闭按钮
            close_buttons = self.device(className="android.widget.ImageButton")
            if close_buttons.exists(timeout=0.5):
                for i in range(close_buttons.count):
                    button = close_buttons[i] if close_buttons.count > 1 else close_buttons
                    if self._is_close_button_by_position(button):
                        log.debug("找到位置合适的ImageButton关闭按钮")
                        button.click()
                        time.sleep(0.5)
                        return True

        except Exception as e:
            log.debug(f"UI层次结构检测失败: {e}")

        return False

    def _try_close_by_position(self) -> bool:
        """通过位置检测关闭按钮（通常在右上角）"""
        try:
            screen_width = self.device.info['displayWidth']
            screen_height = self.device.info['displayHeight']

            # 检查右上角区域的可点击元素
            right_top_elements = self.device(
                clickable=True,
                bounds=(
                    int(screen_width * 0.8), 0,
                    screen_width, int(screen_height * 0.3)
                )
            )

            if right_top_elements.exists(timeout=0.5):
                log.debug("在右上角找到可点击元素，尝试点击")
                right_top_elements.click()
                time.sleep(0.5)
                return True

        except Exception as e:
            log.debug(f"位置检测失败: {e}")

        return False

    def _try_back_key(self) -> bool:
        """尝试使用返回键关闭弹窗"""
        try:
            log.debug("尝试使用返回键关闭弹窗")
            self.device.press("back")
            time.sleep(0.5)
            return True
        except Exception as e:
            log.debug(f"返回键操作失败: {e}")
            return False

    def _is_close_button_by_position(self, element) -> bool:
        """根据位置判断是否为关闭按钮"""
        try:
            bounds = element.info.get('bounds', {})
            if not bounds:
                return False

            screen_width = self.device.info['displayWidth']
            element_right = bounds.get('right', 0)

            # 检查是否在屏幕右侧80%区域
            return element_right > screen_width * 0.8

        except Exception:
            return False

    def is_popup_present(self) -> bool:
        """检测是否有弹窗出现"""
        try:
            # 方法1: 检查常见的弹窗类型
            for class_name in self.popup_classes:
                if self.device(className=class_name).exists(timeout=0.5):
                    log.debug(f"检测到弹窗类型: {class_name}")
                    return True

            # 方法2: 检查是否有包含关键词的文本元素
            popup_keywords = self.close_texts
            
            for keyword in popup_keywords:
                if self.device(textContains=keyword).exists(timeout=0.5):
                    log.debug(f"检测到弹窗关键词: {keyword}")
                    return True

        except Exception as e:
            log.debug(f"弹窗检测失败: {e}")

        return False

    def safe_action(self, action_func: Callable, *args, **kwargs) -> Any:
        """
        执行操作前后检查并处理弹窗
        
        Args:
            action_func: 要执行的操作函数
            *args: 操作函数的位置参数
            **kwargs: 操作函数的关键字参数
            
        Returns:
            操作函数的返回值
        """
        # 执行操作前检查弹窗
        self.detect_and_close_popup(timeout=3)

        # 执行主要操作
        try:
            result = action_func(*args, **kwargs)
        except Exception as e:
            log.error(f"执行操作失败: {e}")
            raise

        # 执行操作后再次检查弹窗
        self.detect_and_close_popup(timeout=5)

        return result

    def click_with_popup_handling(self, x: int, y: int) -> bool:
        """
        带弹窗处理的点击操作
        
        Args:
            x: 点击的x坐标
            y: 点击的y坐标
            
        Returns:
            bool: 操作是否成功
        """
        try:
            return self.safe_action(self.device.click, x, y)
        except Exception as e:
            log.error(f"点击操作失败: {e}")
            return False

    def input_with_popup_handling(self, text: str) -> bool:
        """
        带弹窗处理的输入操作
        
        Args:
            text: 要输入的文本
            
        Returns:
            bool: 操作是否成功
        """
        try:
            return self.safe_action(self.device.send_keys, text)
        except Exception as e:
            log.error(f"输入操作失败: {e}")
            return False

    def element_click_with_popup_handling(self, selector: Dict[str, Any]) -> bool:
        """
        带弹窗处理的元素点击操作
        
        Args:
            selector: 元素选择器字典，如 {'resourceId': 'com.example:id/button'}
            
        Returns:
            bool: 操作是否成功
        """
        try:
            def click_element():
                element = self.device(**selector)
                if element.exists(timeout=10):
                    element.click()
                    return True
                else:
                    raise TimeoutError(f"元素未找到: {selector}")
            
            return self.safe_action(click_element)
        except Exception as e:
            log.error(f"元素点击操作失败: {e}")
            return False


class AutomationWithPopupTool:
    """集成弹窗处理的自动化操作类"""

    def __init__(self, device: u2.Device = None):
        """
        初始化自动化操作类
        
        Args:
            device: UIAutomator2设备对象
        """
        if device is None:
            self.device = u2.connect()
        else:
            self.device = device
        
        self.popup_tool = PopupTool(self.device)

    def click(self, x: int, y: int) -> bool:
        """带弹窗处理的点击"""
        return self.popup_tool.click_with_popup_handling(x, y)

    def input_text(self, text: str) -> bool:
        """带弹窗处理的输入"""
        return self.popup_tool.input_with_popup_handling(text)

    def click_element(self, selector: Dict[str, Any]) -> bool:
        """带弹窗处理的元素点击"""
        return self.popup_tool.element_click_with_popup_handling(selector)

    def detect_and_close_popup(self, timeout: int = 10) -> bool:
        """检测并关闭弹窗"""
        return self.popup_tool.detect_and_close_popup(timeout)

    def is_popup_present(self) -> bool:
        """检测是否有弹窗"""
        return self.popup_tool.is_popup_present()


# 便捷函数
def create_popup_tool(device: u2.Device = None) -> PopupTool:
    """
    创建弹窗处理工具实例

    Args:
        device: UIAutomator2设备对象，如果为None则使用默认连接

    Returns:
        PopupTool: 弹窗处理工具实例
    """
    return PopupTool(device)


def create_automation_with_popup(device: u2.Device = None) -> AutomationWithPopupTool:
    """
    创建集成弹窗处理的自动化操作实例

    Args:
        device: UIAutomator2设备对象，如果为None则使用默认连接

    Returns:
        AutomationWithPopupTool: 自动化操作实例
    """
    return AutomationWithPopupTool(device)


# 使用示例
if __name__ == "__main__":
    # 示例1: 基本弹窗处理
    from core.base_driver import driver_manager


    popup_tool = create_popup_tool(driver_manager.driver)

    # # 检测并关闭弹窗
    # if popup_tool.detect_and_close_popup(timeout=10):
    #     log.info("成功处理弹窗")
    # else:
    #     log.info("未发现弹窗或处理失败")

    # # 示例2: 带弹窗处理的自动化操作
    # automation = create_automation_with_popup()
    #
    # # 执行点击操作，自动处理弹窗
    # automation.click(500, 300)
    #
    # # 执行输入操作，自动处理弹窗
    # automation.input_text("测试文本")
    #
    # # 点击元素，自动处理弹窗
    # automation.click_element({'resourceId': 'com.example:id/button'})
    #
    # # 示例3: 手动控制弹窗处理
    # popup_tool = create_popup_tool()
    #
    # 检查是否有弹窗
    if popup_tool.is_popup_present():
        log.info("检测到弹窗")
        # 尝试关闭弹窗
        if popup_tool.detect_and_close_popup():
            log.info("弹窗已关闭")
    #
    # # 示例4: 在现有操作中集成弹窗处理
    # def my_custom_operation():
    #     """自定义操作示例"""
    #     # 执行一些操作
    #     popup_tool.device.click(100, 200)
    #     time.sleep(1)
    #     popup_tool.device.send_keys("hello")
    #     return "操作完成"
    #
    # # 使用safe_action包装自定义操作
    # result = popup_tool.safe_action(my_custom_operation)
